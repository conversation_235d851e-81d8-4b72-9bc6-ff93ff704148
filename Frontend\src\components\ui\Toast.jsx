import { createContext, useContext, useState, useEffect } from "react"
import { cn } from "../../lib/utils"

const ToastContext = createContext()

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([])

  const addToast = ({ title, description, variant = "default", duration = 5000 }) => {
    const id = Math.random().toString(36).substr(2, 9)
    const toast = { id, title, description, variant, duration }
    
    setToasts(prev => [...prev, toast])
    
    if (duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, duration)
    }
    
    return id
  }

  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const toast = {
    success: (props) => addToast({ ...props, variant: "success" }),
    error: (props) => addToast({ ...props, variant: "error" }),
    warning: (props) => addToast({ ...props, variant: "warning" }),
    info: (props) => addToast({ ...props, variant: "info" }),
    default: (props) => addToast({ ...props, variant: "default" })
  }

  return (
    <ToastContext.Provider value={{ toast, addToast, removeToast }}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  )
}

const ToastContainer = ({ toasts, removeToast }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toasts.map(toast => (
        <ToastItem key={toast.id} toast={toast} onRemove={() => removeToast(toast.id)} />
      ))}
    </div>
  )
}

const ToastItem = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10)
    return () => clearTimeout(timer)
  }, [])

  const handleRemove = () => {
    setIsLeaving(true)
    setTimeout(onRemove, 300) // Wait for exit animation
  }

  const variants = {
    default: {
      bg: "bg-white dark:bg-gray-800",
      border: "border-gray-200 dark:border-gray-700",
      icon: "text-blue-500",
      iconBg: "bg-blue-100 dark:bg-blue-900/30"
    },
    success: {
      bg: "bg-white dark:bg-gray-800",
      border: "border-emerald-200 dark:border-emerald-700",
      icon: "text-emerald-500",
      iconBg: "bg-emerald-100 dark:bg-emerald-900/30"
    },
    error: {
      bg: "bg-white dark:bg-gray-800",
      border: "border-red-200 dark:border-red-700",
      icon: "text-red-500",
      iconBg: "bg-red-100 dark:bg-red-900/30"
    },
    warning: {
      bg: "bg-white dark:bg-gray-800",
      border: "border-amber-200 dark:border-amber-700",
      icon: "text-amber-500",
      iconBg: "bg-amber-100 dark:bg-amber-900/30"
    },
    info: {
      bg: "bg-white dark:bg-gray-800",
      border: "border-cyan-200 dark:border-cyan-700",
      icon: "text-cyan-500",
      iconBg: "bg-cyan-100 dark:bg-cyan-900/30"
    }
  }

  const variant = variants[toast.variant] || variants.default

  const getIcon = () => {
    switch (toast.variant) {
      case "success":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case "error":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
      case "warning":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        )
      case "info":
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  return (
    <div
      className={cn(
        "flex items-start p-4 rounded-lg border shadow-lg backdrop-blur-sm transition-all duration-300 transform",
        variant.bg,
        variant.border,
        isVisible && !isLeaving ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
        isLeaving && "-translate-x-full opacity-0"
      )}
    >
      <div className={cn("flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center", variant.iconBg)}>
        <div className={variant.icon}>
          {getIcon()}
        </div>
      </div>
      
      <div className="ml-3 flex-1">
        {toast.title && (
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
            {toast.title}
          </h4>
        )}
        {toast.description && (
          <p className={cn(
            "text-sm text-gray-600 dark:text-gray-400",
            toast.title && "mt-1"
          )}>
            {toast.description}
          </p>
        )}
      </div>
      
      <button
        onClick={handleRemove}
        className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  )
}
