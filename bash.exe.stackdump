Stack trace:
Frame         Function      Args
0007FFFF78F0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF78F0, 0007FFFF67F0) msys-2.0.dll+0x1FEBA
0007FFFF78F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7BC8) msys-2.0.dll+0x67F9
0007FFFF78F0  000210046832 (000210285FF9, 0007FFFF77A8, 0007FFFF78F0, 000000000000) msys-2.0.dll+0x6832
0007FFFF78F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF78F0  0002100690B4 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF7BD0  00021006A49D (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF94A3A0000 ntdll.dll
7FF94A1D0000 KERNEL32.DLL
7FF947810000 KERNELBASE.dll
7FF948140000 USER32.dll
000210040000 msys-2.0.dll
7FF9474F0000 win32u.dll
7FF94A330000 GDI32.dll
7FF947EB0000 gdi32full.dll
7FF9476A0000 msvcp_win.dll
7FF947FF0000 ucrtbase.dll
7FF948E40000 advapi32.dll
7FF948FB0000 msvcrt.dll
7FF948F00000 sechost.dll
7FF948BF0000 RPCRT4.dll
7FF946C10000 CRYPTBASE.DLL
7FF947E10000 bcryptPrimitives.dll
7FF949860000 IMM32.DLL
