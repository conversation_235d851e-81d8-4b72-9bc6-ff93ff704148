import { forwardRef, useState } from "react"
import { cn } from "../../lib/utils"

const Avatar = forwardRef(({ 
  className, 
  src, 
  alt, 
  fallback, 
  size = "default",
  status,
  ...props 
}, ref) => {
  const [imageError, setImageError] = useState(false)
  
  const sizes = {
    sm: "h-8 w-8 text-xs",
    default: "h-10 w-10 text-sm",
    lg: "h-12 w-12 text-base",
    xl: "h-16 w-16 text-lg",
    "2xl": "h-20 w-20 text-xl"
  }
  
  const statusColors = {
    online: "bg-emerald-500",
    offline: "bg-gray-400",
    away: "bg-amber-500",
    busy: "bg-red-500"
  }
  
  const getInitials = (name) => {
    if (!name) return "?"
    return name
      .split(" ")
      .map(word => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }
  
  return (
    <div className={cn("relative inline-block", className)} ref={ref} {...props}>
      <div
        className={cn(
          "flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold shadow-lg",
          sizes[size]
        )}
      >
        {src && !imageError ? (
          <img
            src={src}
            alt={alt}
            className="h-full w-full rounded-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <span className="select-none">
            {fallback || getInitials(alt)}
          </span>
        )}
      </div>
      
      {status && (
        <span
          className={cn(
            "absolute bottom-0 right-0 block rounded-full ring-2 ring-white dark:ring-gray-800",
            statusColors[status],
            size === "sm" ? "h-2 w-2" : 
            size === "default" ? "h-2.5 w-2.5" :
            size === "lg" ? "h-3 w-3" :
            size === "xl" ? "h-4 w-4" : "h-5 w-5"
          )}
        />
      )}
    </div>
  )
})

Avatar.displayName = "Avatar"

const AvatarGroup = forwardRef(({ className, children, max = 3, ...props }, ref) => {
  const childrenArray = Array.isArray(children) ? children : [children]
  const visibleChildren = childrenArray.slice(0, max)
  const remainingCount = childrenArray.length - max
  
  return (
    <div
      ref={ref}
      className={cn("flex -space-x-2", className)}
      {...props}
    >
      {visibleChildren.map((child, index) => (
        <div key={index} className="ring-2 ring-white dark:ring-gray-800 rounded-full">
          {child}
        </div>
      ))}
      
      {remainingCount > 0 && (
        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm font-medium ring-2 ring-white dark:ring-gray-800">
          +{remainingCount}
        </div>
      )}
    </div>
  )
})

AvatarGroup.displayName = "AvatarGroup"

export { Avatar, AvatarGroup }
