import { createContext, useContext, useState, useEffect } from "react"

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}

// Theme configuration
const themes = {
  light: {
    name: "light",
    colors: {
      primary: "59 130 246", // blue-500
      secondary: "16 185 129", // emerald-500
      accent: "139 92 246", // violet-500
      background: "255 255 255", // white
      foreground: "17 24 39", // gray-900
      muted: "249 250 251", // gray-50
      border: "229 231 235", // gray-200
    }
  },
  dark: {
    name: "dark",
    colors: {
      primary: "59 130 246", // blue-500
      secondary: "16 185 129", // emerald-500
      accent: "139 92 246", // violet-500
      background: "17 24 39", // gray-900
      foreground: "243 244 246", // gray-100
      muted: "31 41 55", // gray-800
      border: "55 65 81", // gray-700
    }
  }
}

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // Check for saved theme preference or default to system preference
    const saved = localStorage.getItem("theme")
    if (saved && (saved === "light" || saved === "dark")) {
      return saved
    }

    // Check system preference
    if (typeof window !== "undefined" && window.matchMedia) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"
    }

    return "light"
  })

  const isDark = theme === "dark"
  const currentTheme = themes[theme]

  useEffect(() => {
    // Save theme preference
    localStorage.setItem("theme", theme)

    // Apply theme to document
    const root = document.documentElement

    if (theme === "dark") {
      root.classList.add("dark")
    } else {
      root.classList.remove("dark")
    }

    // Apply CSS custom properties
    Object.entries(currentTheme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute("content", theme === "dark" ? "#1f2937" : "#ffffff")
    }
  }, [theme, currentTheme])

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === "undefined" || !window.matchMedia) return

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)")
    const handleChange = (e) => {
      // Only auto-switch if user hasn't manually set a preference
      const saved = localStorage.getItem("theme")
      if (!saved) {
        setTheme(e.matches ? "dark" : "light")
      }
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  const toggleTheme = () => {
    setTheme(prev => prev === "dark" ? "light" : "dark")
  }

  const setLightTheme = () => setTheme("light")
  const setDarkTheme = () => setTheme("dark")

  const value = {
    theme,
    isDark,
    currentTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setTheme
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}
